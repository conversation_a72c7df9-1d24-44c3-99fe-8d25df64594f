apiVersion: batch/v1
kind: CronJob
metadata:
  name: task-status-processor
  namespace: webjobs
  labels:
    app.kubernetes.io/part-of: hccb
spec:
  successfulJobsHistoryLimit: 0
  failedJobsHistoryLimit:  1
  timeZone: UTC
  schedule: "30 20 * * *"
  jobTemplate:
    spec:
      backoffLimit: 0
      template:
        spec:
          restartPolicy: Never
          containers:
          - name: task-status-processor
            image: fahccb.azurecr.io/hccb-task-status-processor:#{Build.BuildNumber}#
            envFrom:
            - secretRef:
                name: task-status-processor-secrets
            resources:
              requests:
                cpu: 400m
                memory: 3G
              limits:
                cpu: 700m
                memory: 4G

