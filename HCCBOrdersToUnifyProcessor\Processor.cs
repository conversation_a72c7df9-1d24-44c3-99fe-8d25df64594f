using Core.Loggers;
using Microsoft.EntityFrameworkCore;
using Core.Abstracts;
using HCCB.DbStorage.DbContexts;
using HCCBOrdersToUnifyProcessor.Configuration;
using Libraries.CommonEnums.HCCB;
using HCCB.DbStorage.DbModel.HccbDbModel;
using Core.Repositories;
using HCCB.DbStorage.DbModel.HccbUnifyDbModel;
using Core.Services;
using Core.Models;
using Library.DateTimeHelpers;
using Core.Models.DTOs;
using Library.CommonHelpers.Extensions;
using System.Reflection.Emit;

namespace HCCBOrdersToUnifyProcessor
{
    public class Processor(ISlackLogHelper slackLogHelper,
        FileLogger fileLogger, 
        WritableHccbDataContext hccbDataContext,
        HCCBUnifyClickhouseSqlDataReader clickHouseDataReader,
        ILastSyncTimeRepository lastSyncTimeRepository,
        MTDService mTDService,
        IPositionCodeRepository positionCodeRepository,
        ILocationsRepository locationsRepository,
        IProductsRepository productsRepository)
        : ProcessorBase(fileLogger, slackLogHelper)
    {
        private readonly WritableHccbDataContext _hccbDataContext = hccbDataContext;
        private readonly HCCBUnifyClickhouseSqlDataReader _clickHouseDataReader = clickHouseDataReader;
        private readonly ILastSyncTimeRepository lastSyncTimeRepository = lastSyncTimeRepository;
        private readonly MTDService mTDService = mTDService;
        private readonly IPositionCodeRepository positionCodeRepository = positionCodeRepository;
        private readonly ILocationsRepository locationsRepository = locationsRepository;
        private readonly IProductsRepository productsRepository = productsRepository;
        private const string ProcessorName = "HCCB Orders To Unify Processor";
        private const string IntegrationId = "HCCBOrdersToUnify";
        private const int initialBatchSize = 10000;
        private const int minBatchSize = 100;
        private const int maxRetryAttempts = 3;
        private static string ChannelId = Dependencies.ChannelId;
        private static List<HccbOrderSource> hccbOrderSources = new List<HccbOrderSource>
        {
            HccbOrderSource.TellSell,
            HccbOrderSource.CokeBuddy
        };

        protected override string LogHeader
        {
            get; set;
        }

        protected override string GetChannelId()
        {
            return ChannelId;
        }

        protected override string GetProcessorName()
        {
            return ProcessorName;
        }

        protected override void _ValidateArguments(params object?[] args)
        {
            return;
        }

        protected override async Task _Process(params object?[] args)
        {
            await ProcessCompanyOrders(Dependencies.CompanyId);
        }

        private async Task ProcessCompanyOrders(long companyId)
        {       
            // Get last sync time
            var lastSyncTime = (await lastSyncTimeRepository.GetLastSyncTimeForIntegration(companyId, IntegrationId))?.LastSyncTime ?? DateTime.UtcNow.AddDays(-90);
                
            // Calculate end time (UTCNOW - 1 minute to handle lag)
            var endTime = DateTime.UtcNow.AddMinutes(-10);
                
            await _slackLogHelper.SendMessageToSlack($"[{LogHeader}]: [{companyId}] Processing orders from {lastSyncTime} to {endTime}");
                
            var userHierarchies = (await positionCodeRepository.GetPositionUserHierarchies(companyId)).ToLookup(h => h.UserId, h => h);

            var productHierarchies = (await productsRepository.GetProductHierarchy(companyId)).ToDictionary(p => p.ProductId, p => p);

            int processedRecordsCount = 0;
            var newLastSyncTime = lastSyncTime;
            var orderNumbersToProcess = await _hccbDataContext.HccbOrders
                                        .Where(o => o.CompanyId == companyId
                                                    && hccbOrderSources.Contains(o.OrderSourceEnum)
                                                    && o.CreatedAt > lastSyncTime
                                                    && o.CreatedAt <= endTime).Select(o => o.DocumentNumber_Header).Distinct().AsNoTracking().ToListAsync();
            var recordsToProcess = orderNumbersToProcess.Count;
            await _slackLogHelper.SendMessageToSlack($"[{LogHeader}]: [{companyId}] Found {recordsToProcess} records to process.");

            var currentBatchSize = initialBatchSize;
            foreach (var batch in orderNumbersToProcess.GetBatches(currentBatchSize))
            {
                await ProcessBatchWithRetry(companyId, batch, userHierarchies, productHierarchies,
                    ref processedRecordsCount, ref newLastSyncTime);
            }

            // Update last sync time
            await lastSyncTimeRepository.UpsertLastSyncTimeForIntegration(companyId, IntegrationId, newLastSyncTime);
            await _slackLogHelper.SendMessageToSlack($"[{LogHeader}]: [{companyId}] Successfully inserted {processedRecordsCount} records into ClickHouse");
        }

        private async Task ProcessBatchWithRetry(long companyId, IEnumerable<string> batch,
            ILookup<long, PositionUserHierarchy> userHierarchies, Dictionary<long, ProductFlat> productHierarchies,
            ref int processedRecordsCount, ref DateTime newLastSyncTime)
        {
            var batchList = batch.ToList();
            var retryCount = 0;
            var currentBatchSize = batchList.Count;

            while (retryCount <= maxRetryAttempts)
            {
                try
                {
                    // If we're retrying due to OOM and batch size can be reduced, split the batch
                    if (retryCount > 0 && currentBatchSize > minBatchSize)
                    {
                        var halfBatchSize = Math.Max(minBatchSize, currentBatchSize / 2);
                        await _slackLogHelper.SendMessageToSlack($"[{LogHeader}]: [{companyId}] OOM retry {retryCount}, reducing batch size from {currentBatchSize} to {halfBatchSize}");

                        // Process first half
                        var firstHalf = batchList.Take(halfBatchSize).ToList();
                        await ProcessSingleBatch(companyId, firstHalf, userHierarchies, productHierarchies, ref processedRecordsCount, ref newLastSyncTime);

                        // Process second half
                        var secondHalf = batchList.Skip(halfBatchSize).ToList();
                        if (secondHalf.Any())
                        {
                            await ProcessSingleBatch(companyId, secondHalf, userHierarchies, productHierarchies, ref processedRecordsCount, ref newLastSyncTime);
                        }
                        return;
                    }
                    else
                    {
                        await ProcessSingleBatch(companyId, batchList, userHierarchies, productHierarchies, ref processedRecordsCount, ref newLastSyncTime);
                        return;
                    }
                }
                catch (OutOfMemoryException ex)
                {
                    retryCount++;
                    await _slackLogHelper.SendMessageToSlack($"[{LogHeader}]: [{companyId}] OutOfMemoryException occurred. Retry attempt {retryCount}/{maxRetryAttempts}. Batch size: {currentBatchSize}");
                    _fileLogger.WriteLine($"OutOfMemoryException on retry {retryCount}: {ex}");

                    if (retryCount > maxRetryAttempts)
                    {
                        await _slackLogHelper.SendMessageToSlack($"[{LogHeader}]: [{companyId}] Max retry attempts exceeded for OOM. Skipping batch of {currentBatchSize} records.");
                        throw new InvalidOperationException($"Failed to process batch after {maxRetryAttempts} OOM retry attempts. Batch size: {currentBatchSize}", ex);
                    }

                    // Aggressive memory cleanup
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();

                    // Exponential backoff delay
                    var delayMs = (int)(Math.Pow(2, retryCount - 1) * 1000); // 1s, 2s, 4s
                    await Task.Delay(delayMs);
                }
                catch (Exception ex)
                {
                    // For non-OOM exceptions, don't retry
                    await _slackLogHelper.SendMessageToSlack($"[{LogHeader}]: [{companyId}] Non-OOM exception occurred: {ex.Message}");
                    throw;
                }
            }
        }

        private async Task ProcessSingleBatch(long companyId, List<string> batch,
            ILookup<long, PositionUserHierarchy> userHierarchies, Dictionary<long, ProductFlat> productHierarchies,
            ref int processedRecordsCount, ref DateTime newLastSyncTime)
        {
            var orders = await _hccbDataContext.HccbOrders
                .Where(o => o.CompanyId == companyId && batch.Contains(o.DocumentNumber_Header))
                .AsNoTracking()
                .ToListAsync();

            await FlushRecordsToClickHouse(companyId, orders, userHierarchies, productHierarchies);
            processedRecordsCount += orders.Count;

            if (orders.Any())
            {
                var newSyncTimeCandidate = orders.Max(o => o.CreatedAt);
                newLastSyncTime = newSyncTimeCandidate > newLastSyncTime ? newSyncTimeCandidate : newLastSyncTime;
            }
        }

        private async Task FlushRecordsToClickHouse(long companyId, List<HccbOrder> orders, ILookup<long, PositionUserHierarchy> userHierarchies,
            Dictionary<long, ProductFlat> productHierarchies)
        {
            var distinctOrderDates = orders.Select(order => order.DocumentDate.Date).Distinct().ToList();

            var locationIds = orders.Select(order => order.F2kLocations_Id).Distinct().ToList();

            var locationMins = (await locationsRepository.GetLocationMinModels(companyId, locationIds)).ToDictionary(l => l.Id, l => l);

            var beatIds = locationMins.Values.Select(l => l.BeatId).Distinct().ToList();

            var locationHierarchy = (await locationsRepository.GetGeoHierarchy(companyId, beatIds)).ToDictionary(b => b.BeatId, b => b);

            var distinctDatesDict = new Dictionary<DateTime, FA_MTD_LMTD>();

            foreach (var date in distinctOrderDates)
            {
                distinctDatesDict.Add(date, await mTDService.GetDates(companyId, date, Dependencies.YearStartMonth, includeToday: true));
            }

            // Transform orders to ProductWiseDemandSales model
            var secondaryDemandData = orders.Select(order => {
                locationMins.TryGetValue(order.F2kLocations_Id, out var locationMin);
                return new SecondaryDemand(order,
                    distinctDatesDict[order.DocumentDate.Date],
                    userHierarchies[order.ClientEmployee_Id].FirstOrDefault() ?? new PositionUserHierarchy(),
                    locationMin ?? new LocationMinModel(),
                    locationHierarchy.TryGetValue(locationMin?.BeatId ?? 0, out var geoHierarchy) ? geoHierarchy : new GeoHierarchy(),
                    productHierarchies.TryGetValue(order.FaCompanyProducts_Id, out var product) ? product : new ProductFlat()
                    );
                }).ToList();

            // Insert into ClickHouse SecondaryDemand table
            var insertedCount = await _clickHouseDataReader.BulkCopyData(secondaryDemandData, "SecondaryDemand");
        }
    }
}
